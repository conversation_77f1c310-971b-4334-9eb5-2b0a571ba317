﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Request
{
    public class FeedbackRequestDto
    {
        public int FeedbackId { get; set; }

        public string Name { get; set; } = null!;

        public string Title { get; set; } = null!;

        public string? Description { get; set; }

        public string? Response { get; set; }

        public string? ImageBase64 { get; set; }
        public string? FileName { get; set; }

        public int? Status { get; set; }

        public int? CreatedBy { get; set; }

        public int? UpdatedBy { get; set; }

        public int? FacilityId { get; set; }

    }
}
