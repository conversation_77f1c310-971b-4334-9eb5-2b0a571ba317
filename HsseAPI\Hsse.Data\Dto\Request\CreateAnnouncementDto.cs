using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Request
{
    public class CreateAnnouncementDto
    {
        public int AnnouncementsId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public int? Status { get; set; }
        public int? CreatedBy { get; set; }
        public int? ModifiedBy { get; set; }
        public string ImageBase64 { get; set; }
        public string Filename { get; set; }
        public DateTime? ScheduleAt { get; set; }
        public DateTime? ExpiryAt { get; set; }
        public int? FacilityID { get; set; }
        public int? CategoryId { get; set; }
        public List<int> ReceiverUserIds { get; set; } = new List<int>();
        public List<int> ReceiverGroupIds { get; set; } = new List<int>();
        public List<CreateAnnouncementDocumentDto> Documents { get; set; } = new List<CreateAnnouncementDocumentDto>();
    }

    public class CreateAnnouncementDocumentDto
    {
        public int DocumentID { get; set; }
        public string DocumentName { get; set; }
        public string DocumentFile { get; set; }
    }
}
